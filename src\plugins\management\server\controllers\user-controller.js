'use strict';

const userController = ({ strapi }) => ({
  async listUsers(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .listUsers(ctx.query);
  },

  async getUserDetail(ctx) {
    const { id } = ctx.params;

    // Validate ID parameter
    const userId = parseInt(id, 10);
    if (isNaN(userId) || userId <= 0) {
      ctx.throw(400, 'Invalid user ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .getUserDetail(userId);
  },

  async updateUserStatus(ctx) {
    const { id } = ctx.params;
    const { blocked } = ctx.request.body;

    // Validate ID parameter
    const userId = parseInt(id, 10);
    if (isNaN(userId) || userId <= 0) {
      ctx.throw(400, 'Invalid user ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .updateUserStatus(userId, blocked);
  },

  async updateUserInfo(ctx) {
    const { id } = ctx.params;
    const userData = ctx.request.body;

    // Validate ID parameter
    const userId = parseInt(id, 10);
    if (isNaN(userId) || userId <= 0) {
      ctx.throw(400, 'Invalid user ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .updateUserInfo(userId, userData);
  },

  async createUser(ctx) {
    const userData = ctx.request.body;
    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .createUser(userData);
  },

  async getUserRoles(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .getUserRoles();
  },

  async getUserStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .getUserStatistics();
  },

  async exportUsersToExcel(ctx) {
    try {
      // Get query parameters from either query string (GET) or request body (POST)
      const queryParams = ctx.method === 'POST' ? ctx.request.body : ctx.query;

      const buffer = await strapi
        .plugin('management')
        .service('user-service')
        .exportUsersToExcel(queryParams);

      // Generate filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS format
      const filename = `dai-ly-${dateStr}-${timeStr}.csv`;

      ctx.set('Content-Type', 'text/csv; charset=utf-8');
      ctx.set('Content-Disposition', `attachment; filename="${filename}"`);
      ctx.body = buffer;
    } catch (error) {
      console.error('Export error:', error);
      ctx.badRequest('Export failed');
    }
  },

  async index(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('user-service')
      .find(ctx.query);
  },
});

module.exports = userController;
