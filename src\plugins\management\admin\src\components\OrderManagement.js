const React = require('react');
const { useState, useEffect } = React;
const dayjs = require('dayjs');
require('dayjs/locale/vi');
const XLSX = require('xlsx');
const {
  ShoppingCart,
  Eye,
  Filter,
  Download,
  RefreshCw,
  Clock,
  Truck,
  CheckCircle2,
  AlertCircle,
  Send,
  ArrowUpDown,
  FileText,
  Gift,
  X,
  Percent,
  Plus,
  Printer,
} = require('lucide-react');
const {
  Modal,
  message,
  Spin,
  Table: AntTable,
  Empty,
  Button: AntButton,
  Row,
  Col,
  Tag,
  Space,
  Tooltip,
  Radio,
  DatePicker,
  Checkbox,
  Badge,
  Tabs,
  Card: AntCard,
  Drawer,
  Select,
  Input,
  AutoComplete,
} = require('antd');
const { RangePicker } = DatePicker;
const { useFetchClient } = require('@strapi/helper-plugin');
const {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  FilterGroup,
  <PERSON><PERSON><PERSON>abe<PERSON>,
  DateInput,
  ActionButtonGroup,
} = require('./shared');
require('./OrderManagement.css');

// Set dayjs locale to Vietnamese
dayjs.locale('vi');

// Order status options - Unified color scheme
const statusOptions = [
  {
    value: '',
    label: 'Tất cả',
    color: '#64748b',
    icon: React.createElement(Filter, { size: 16 }),
  },
  {
    value: 'Chờ xác nhận',
    label: 'Chờ xác nhận',
    color: '#f59e0b',
    icon: React.createElement(Clock, { size: 16 }),
  },
  {
    value: 'Chờ giao hàng',
    label: 'Chờ giao hàng',
    color: '#3b82f6',
    icon: React.createElement(Send, { size: 16 }),
  },
  {
    value: 'Đang giao hàng',
    label: 'Đang giao hàng',
    color: '#8b5cf6',
    icon: React.createElement(Truck, { size: 16 }),
  },
  {
    value: 'Đã hoàn thành',
    label: 'Đã hoàn thành',
    color: '#10b981',
    icon: React.createElement(CheckCircle2, { size: 16 }),
  },
  {
    value: 'Đã hủy',
    label: 'Đã hủy',
    color: '#ef4444',
    icon: React.createElement(AlertCircle, { size: 16 }),
  },
];

// Define valid status transitions
const statusTransitions = {
  'Chờ xác nhận': ['Chờ giao hàng', 'Đã hủy'],
  'Chờ giao hàng': ['Đang giao hàng', 'Đã hủy'],
  'Đang giao hàng': ['Đã hoàn thành', 'Đã hủy'],
  'Đã hoàn thành': [],
  'Đã hủy': [],
};

const OrderManagement = function () {
  const { get, put, post } = useFetchClient();

  // State management
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [exporting, setExporting] = useState(false);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    status: 'Chờ xác nhận',
    dateFrom: '',
    dateTo: '',
  });

  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    pageCount: 0,
  });

  // Stats
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    shippingOrders: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    totalRevenue: 0,
  });

  // Export modal states
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportDateRange, setExportDateRange] = useState(null);
  const [exportStatuses, setExportStatuses] = useState([]);

  // Order detail modal states
  const [orderDetailVisible, setOrderDetailVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDetail, setOrderDetail] = useState(null);
  const [orderDetailLoading, setOrderDetailLoading] = useState(false);

  // Create order modal states
  const [createOrderVisible, setCreateOrderVisible] = useState(false);
  const [creating, setCreating] = useState(false);

  // User selection states
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [customerFormData, setCustomerFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    ward: '',
    district: '',
    city: '',
  });

  // Product selection states
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [productSearchTerm, setProductSearchTerm] = useState('');

  // Order calculation states
  const [taxPercentage, setTaxPercentage] = useState(0);

  // Helper functions
  const getStatusConfig = function (status) {
    return (
      statusOptions.find(function (opt) {
        return opt.value === status;
      }) || statusOptions[0]
    );
  };

  const formatCurrency = function (amount) {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  const getAvailableStatusOptions = function (currentStatus) {
    const availableStatuses = statusTransitions[currentStatus] || [];
    return statusOptions.filter(function (option) {
      return availableStatuses.includes(option.value);
    });
  };

  // Order steps helper function
  const getOrderSteps = function (currentStatus) {
    const steps = [
      {
        title: 'Chờ duyệt',
        description: 'Đơn hàng đang chờ xác nhận',
        icon: React.createElement(FileText, { size: 16 }),
        status: 'Chờ xác nhận',
        color: '#f59e0b',
      },
      {
        title: 'Chờ giao hàng',
        description: 'Đơn hàng đã được xác nhận, chuẩn bị giao',
        icon: React.createElement(Clock, { size: 16 }),
        status: 'Chờ giao hàng',
        color: '#3b82f6',
      },
      {
        title: 'Đang giao hàng',
        description: 'Đơn hàng đang được vận chuyển',
        icon: React.createElement(Truck, { size: 16 }),
        status: 'Đang giao hàng',
        color: '#8b5cf6',
      },
      {
        title: 'Hoàn thành',
        description: 'Đơn hàng đã được giao thành công',
        icon: React.createElement(Gift, { size: 16 }),
        status: 'Đã hoàn thành',
        color: '#10b981',
      },
    ];

    // Handle cancelled orders
    if (currentStatus === 'Đã hủy') {
      return [
        {
          title: 'Đã hủy',
          description: 'Đơn hàng đã bị hủy',
          icon: React.createElement(X, { size: 16 }),
          stepStatus: 'error',
          status: 'Đã hủy',
          color: '#ef4444',
          isActive: true,
        },
      ];
    }

    // Find current step index based on current status
    let currentStepIndex = 0;
    switch (currentStatus) {
      case 'Chờ xác nhận':
        currentStepIndex = 0;
        break;
      case 'Chờ giao hàng':
        currentStepIndex = 1;
        break;
      case 'Đang giao hàng':
        currentStepIndex = 2;
        break;
      case 'Đã hoàn thành':
        currentStepIndex = 3;
        break;
      default:
        currentStepIndex = 0;
    }

    return steps.map(function (step, index) {
      return {
        ...step,
        stepStatus:
          index < currentStepIndex
            ? 'finish'
            : index === currentStepIndex
            ? 'process'
            : 'wait',
        isActive: index === currentStepIndex,
      };
    });
  };

  // Data fetching functions
  const fetchOrders = async function () {
    setLoading(true);
    try {
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
      };

      if (filters.status) params.status = filters.status;
      if (filters.search) params.code = filters.search;
      if (filters.dateFrom) params.startDate = filters.dateFrom;
      if (filters.dateTo) params.endDate = filters.dateTo;

      const { data } = await get('/management/orders', { params });
      setOrders(data.data || []);
      setPagination(function (prev) {
        return {
          ...prev,
          total: data.total || 0,
          pageCount: data.pageCount || 0,
        };
      });
    } catch (error) {
      console.error('Error fetching orders:', error);
      message.error('Không thể tải danh sách đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllOrders = async function () {
    try {
      const { data } = await get('/management/orders', {
        params: { page: 1, pageSize: 1000 },
      });
      const allOrdersData = data.data || [];

      // Calculate stats
      const newStats = {
        totalOrders: allOrdersData.length,
        pendingOrders: allOrdersData.filter(function (o) {
          return o.statusOrder === 'Chờ xác nhận';
        }).length,
        shippingOrders: allOrdersData.filter(function (o) {
          return o.statusOrder === 'Đang giao hàng';
        }).length,
        completedOrders: allOrdersData.filter(function (o) {
          return o.statusOrder === 'Đã hoàn thành';
        }).length,
        cancelledOrders: allOrdersData.filter(function (o) {
          return o.statusOrder === 'Đã hủy';
        }).length,
        totalRevenue: allOrdersData
          .filter(function (o) {
            return o.statusOrder === 'Đã hoàn thành';
          })
          .reduce(function (sum, o) {
            return sum + (o.priceAfterTax || 0);
          }, 0),
      };
      setStats(newStats);
    } catch (error) {
      console.error('Error fetching all orders:', error);
    }
  };

  const handleRefresh = async function () {
    setRefreshing(true);
    try {
      await Promise.all([fetchOrders(), fetchAllOrders()]);
      message.success('Đã làm mới danh sách đơn hàng');
    } catch (error) {
      message.error('Không thể làm mới dữ liệu');
    } finally {
      setRefreshing(false);
    }
  };

  const handleStatusChange = async function (id, newStatus) {
    try {
      await put(`/management/orders/${id}/status`, { status: newStatus });
      message.success('Cập nhật trạng thái thành công');
      await Promise.all([fetchOrders(), fetchAllOrders()]);
    } catch (error) {
      console.error('Error updating order status:', error);
      message.error('Không thể cập nhật trạng thái đơn hàng');
    }
  };

  // Fetch order detail
  const fetchOrderDetail = async function (orderId) {
    setOrderDetailLoading(true);
    try {
      const { data } = await get(`/management/orders/${orderId}`);
      setSelectedOrder(data);
      setOrderDetail(data);
    } catch (error) {
      console.error('Error fetching order detail:', error);
      message.error('Không thể tải chi tiết đơn hàng');
    } finally {
      setOrderDetailLoading(false);
    }
  };

  // Handle view order detail
  const handleViewOrder = async function (orderId) {
    setOrderDetailVisible(true);
    await fetchOrderDetail(orderId);
  };

  // Fetch users for customer selection
  const fetchUsers = async function () {
    setUsersLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '1000',
        blocked: 'false',
      });

      const response = await get('/management/users?' + queryParams);
      console.log('Users response:', response.data);
      setUsers(response.data.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      message.error('Không thể tải danh sách đại lý');
    } finally {
      setUsersLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = function (userId) {
    const selectedUser = users.find(function (user) {
      return user.id === userId;
    });
    if (selectedUser) {
      setSelectedUserId(userId);
      setCustomerFormData({
        name: selectedUser.name || '',
        phone: selectedUser.phone || '',
        email: selectedUser.email || '',
        address: '',
        ward: '',
        district: '',
        city: '',
      });
    }
  };

  // Reset customer form
  const resetCustomerForm = function () {
    setSelectedUserId(null);
    setCustomerFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      ward: '',
      district: '',
      city: '',
    });
  };

  // Helper function to sanitize search term
  const sanitizeSearchTerm = function (term) {
    if (!term) return '';

    // Remove or escape special characters that might cause issues
    return term
      .trim()
      .replace(/[<>]/g, '') // Remove < and > characters
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[\\]/g, '') // Remove backslashes
      .replace(/[{}]/g, '') // Remove curly braces
      .replace(/[\[\]]/g, '') // Remove square brackets
      .replace(/[|]/g, '') // Remove pipe characters
      .replace(/[&]/g, '') // Remove ampersand
      .substring(0, 100); // Limit length to prevent overly long queries
  };

  // Fetch products for selection
  const fetchProducts = async function (searchTerm) {
    searchTerm = searchTerm || '';
    setProductsLoading(true);
    try {
      // Sanitize search term
      const sanitizedTerm = sanitizeSearchTerm(searchTerm);

      // Skip search if term is too short after sanitization
      if (searchTerm && sanitizedTerm.length < 1) {
        setProducts([]);
        setProductsLoading(false);
        return;
      }

      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '100',
        status: 'true', // Only active products
      });

      if (sanitizedTerm) {
        queryParams.set('search', sanitizedTerm);
      }

      const response = await get('/management/products?' + queryParams);
      setProducts(response.data.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      message.error('Không thể tải danh sách sản phẩm');
      setProducts([]); // Clear products on error
    } finally {
      setProductsLoading(false);
    }
  };

  // Handle product selection
  const handleProductSelect = function (productId, quantity) {
    quantity = quantity || 1;
    const product = products.find(function (p) {
      return p.id === productId;
    });
    if (product) {
      const imageUrl =
        product.hinh_anh && product.hinh_anh.length > 0
          ? product.hinh_anh[0]?.formats?.thumbnail?.url ||
            product.hinh_anh[0]?.formats?.small?.url ||
            product.hinh_anh[0]?.url
          : null;

      const existingIndex = selectedProducts.findIndex(function (p) {
        return p.id === productId;
      });
      if (existingIndex >= 0) {
        // Update existing product quantity
        const updatedProducts = [...selectedProducts];
        updatedProducts[existingIndex] = {
          ...updatedProducts[existingIndex],
          quantity: quantity,
          total: product.gia_ban * quantity,
        };
        setSelectedProducts(updatedProducts);
      } else {
        // Add new product
        setSelectedProducts([
          ...selectedProducts,
          {
            id: product.id,
            name: product.name,
            price: product.gia_ban || 0,
            quantity: quantity,
            total: (product.gia_ban || 0) * quantity,
            imageUrl: imageUrl,
          },
        ]);
      }
    }
  };

  // Remove product from selection
  const handleRemoveProduct = function (productId) {
    setSelectedProducts(
      selectedProducts.filter(function (p) {
        return p.id !== productId;
      })
    );
  };

  // Calculate order totals
  const calculateOrderTotals = function () {
    const subtotal = selectedProducts.reduce(function (sum, product) {
      return sum + product.total;
    }, 0);
    const tax = (subtotal * taxPercentage) / 100;
    const total = subtotal + tax;
    return { subtotal: subtotal, tax: tax, total: total };
  };

  // Handle create order
  const handleCreateOrder = async function () {
    setCreating(true);
    try {
      // Validate form data
      if (!customerFormData.name.trim()) {
        message.error('Vui lòng nhập tên khách hàng');
        return;
      }

      if (!customerFormData.phone.trim()) {
        message.error('Vui lòng nhập số điện thoại khách hàng');
        return;
      }

      if (selectedProducts.length === 0) {
        message.error('Vui lòng chọn ít nhất một sản phẩm');
        return;
      }

      setCreateOrderLoading(true);

      // Prepare order data
      const orderData = {
        customer: customerFormData,
        products: selectedProducts.map(function (product) {
          return {
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: product.quantity,
          };
        }),
        taxPercentage: taxPercentage,
        userId: selectedUserId,
      };

      // Create order
      await post('/management/orders', { orderData: orderData });

      message.success('Tạo đơn hàng thành công');

      // Close drawer and reset form
      setCreateOrderVisible(false);
      resetCustomerForm();
      setSelectedProducts([]);
      setProductSearchTerm('');
      setTaxPercentage(0);

      // Refresh orders list
      await Promise.all([fetchOrders(), fetchAllOrders()]);
    } catch (error) {
      console.error('Error creating order:', error);
      message.error('Không thể tạo đơn hàng');
    } finally {
      setCreating(false);
    }
  };

  // Handle export confirm
  const handleExportConfirm = async function () {
    setExporting(true);
    try {
      const queryParams = {
        page: 1,
        pageSize: 10000,
      };

      if (exportDateRange && exportDateRange[0] && exportDateRange[1]) {
        queryParams.startDate = exportDateRange[0].format('YYYY-MM-DD');
        queryParams.endDate = exportDateRange[1].format('YYYY-MM-DD');
      }

      if (exportStatuses.length === 1) {
        queryParams.status = exportStatuses[0];
      }

      const response = await get('/management/orders', { params: queryParams });
      let ordersToExport = response.data.data || [];

      if (exportStatuses.length > 1) {
        ordersToExport = ordersToExport.filter(function (order) {
          return exportStatuses.includes(order.statusOrder);
        });
      }

      if (ordersToExport.length === 0) {
        message.warning('Không có đơn hàng nào phù hợp với bộ lọc đã chọn');
        setExportModalVisible(false);
        return;
      }

      await createAndDownloadExcel(ordersToExport);
      setExportModalVisible(false);
      message.success(
        'Đã tải xuống file Excel với ' + ordersToExport.length + ' đơn hàng!'
      );
    } catch (error) {
      console.error('Error exporting orders:', error);
      message.error('Không thể xuất file Excel');
    } finally {
      setExporting(false);
    }
  };

  // Excel export function
  const createAndDownloadExcel = async function (orders) {
    const workbook = XLSX.utils.book_new();

    // Prepare data for Excel
    const excelData = orders.map(function (order, index) {
      return {
        STT: index + 1,
        'Mã đơn hàng': order.code || '',
        'Trạng thái': order.statusOrder || '',
        'Tên khách hàng': order.customer?.name || '',
        'Số điện thoại': order.customer?.phone || '',
        Email: order.customer?.email || '',
        'Địa chỉ': order.customer?.address || '',
        'Phường/Xã': order.customer?.ward || '',
        'Quận/Huyện': order.customer?.district || '',
        'Tỉnh/Thành phố': order.customer?.city || '',
        'Tổng tiền (trước thuế)': order.priceBeforeTax || 0,
        Thuế: order.taxAmount || 0,
        'Tổng tiền (sau thuế)': order.priceAfterTax || 0,
        'Ngày tạo': order.createdAt
          ? dayjs(order.createdAt).format('DD/MM/YYYY HH:mm')
          : '',
        'Ngày cập nhật': order.updatedAt
          ? dayjs(order.updatedAt).format('DD/MM/YYYY HH:mm')
          : '',
      };
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 5 }, // STT
      { wch: 15 }, // Mã đơn hàng
      { wch: 15 }, // Trạng thái
      { wch: 20 }, // Tên khách hàng
      { wch: 15 }, // Số điện thoại
      { wch: 25 }, // Email
      { wch: 30 }, // Địa chỉ
      { wch: 15 }, // Phường/Xã
      { wch: 15 }, // Quận/Huyện
      { wch: 15 }, // Tỉnh/Thành phố
      { wch: 15 }, // Tổng tiền (trước thuế)
      { wch: 10 }, // Thuế
      { wch: 15 }, // Tổng tiền (sau thuế)
      { wch: 18 }, // Ngày tạo
      { wch: 18 }, // Ngày cập nhật
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Đơn hàng');

    // Generate filename with current date
    const fileName = 'don-hang-' + dayjs().format('YYYY-MM-DD-HH-mm') + '.xlsx';

    // Download file
    XLSX.writeFile(workbook, fileName);
  };

  // Stats data for display - Unified color scheme
  const statsData = [
    {
      title: 'Tổng đơn hàng',
      value: stats.totalOrders,
      icon: ShoppingCart,
      color: '#64748b',
    },
    {
      title: 'Chờ xác nhận',
      value: stats.pendingOrders,
      icon: Clock,
      color: '#f59e0b',
    },
    {
      title: 'Đang giao hàng',
      value: stats.shippingOrders,
      icon: Truck,
      color: '#8b5cf6',
    },
    {
      title: 'Hoàn thành',
      value: stats.completedOrders,
      icon: CheckCircle2,
      color: '#10b981',
    },
  ];

  // Status Update Component
  const StatusUpdateButton = function (props) {
    const { currentStatus, orderId, orderCode } = props;
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState('');
    const [updating, setUpdating] = useState(false);

    const availableOptions = getAvailableStatusOptions(currentStatus);
    const canUpdate = availableOptions.length > 0;

    const handleUpdate = async function () {
      if (!selectedStatus) return;

      setUpdating(true);
      try {
        await handleStatusChange(orderId, selectedStatus);
        setIsModalVisible(false);
        setSelectedStatus('');
      } catch (error) {
        console.error('Error updating status:', error);
      } finally {
        setUpdating(false);
      }
    };

    if (!canUpdate) {
      return React.createElement(
        Tooltip,
        { title: 'Không thể thay đổi trạng thái' },
        React.createElement(
          AntButton,
          {
            type: 'text',
            icon: React.createElement(ArrowUpDown, { size: 14 }),
            size: 'small',
            disabled: true,
          },
          'Cập nhật'
        )
      );
    }

    return React.createElement(
      React.Fragment,
      null,
      React.createElement(
        Tooltip,
        { title: 'Cập nhật trạng thái đơn hàng' },
        React.createElement(
          AntButton,
          {
            type: 'text',
            icon: React.createElement(ArrowUpDown, { size: 14 }),
            size: 'small',
            style: { color: '#1890ff' },
            onClick: function () {
              setIsModalVisible(true);
            },
          },
          'Cập nhật'
        )
      ),
      React.createElement(
        Modal,
        {
          title: 'Cập nhật trạng thái đơn hàng',
          open: isModalVisible,
          onCancel: function () {
            setIsModalVisible(false);
            setSelectedStatus('');
          },
          onOk: handleUpdate,
          confirmLoading: updating,
          okText: 'Cập nhật',
          cancelText: 'Hủy',
        },
        React.createElement(
          'div',
          { style: { marginBottom: 16 } },
          React.createElement(
            'div',
            { style: { fontWeight: 500, marginBottom: 8 } },
            'Đơn hàng: ',
            React.createElement(
              'span',
              { style: { color: '#1890ff' } },
              orderCode
            )
          ),
          React.createElement(
            'div',
            { style: { marginBottom: 16 } },
            'Trạng thái hiện tại:',
            React.createElement(
              Tag,
              {
                color: getStatusConfig(currentStatus).color,
                style: { marginLeft: 8 },
              },
              currentStatus
            )
          )
        ),
        React.createElement(
          'div',
          { style: { marginBottom: 16 } },
          React.createElement(
            'div',
            { style: { fontWeight: 500, marginBottom: 12 } },
            'Chọn trạng thái mới:'
          ),
          React.createElement(
            Radio.Group,
            {
              value: selectedStatus,
              onChange: function (e) {
                setSelectedStatus(e.target.value);
              },
              style: { width: '100%' },
            },
            React.createElement(
              Space,
              { direction: 'vertical', style: { width: '100%' } },
              availableOptions.map(function (option) {
                return React.createElement(
                  Radio,
                  { key: option.value, value: option.value },
                  React.createElement(
                    'div',
                    {
                      style: { display: 'flex', alignItems: 'center', gap: 8 },
                    },
                    option.icon,
                    React.createElement('span', null, option.label)
                  )
                );
              })
            )
          )
        )
      )
    );
  };

  // Effects
  useEffect(
    function () {
      fetchOrders();
    },
    [pagination.page, pagination.pageSize, filters]
  );

  useEffect(function () {
    fetchAllOrders();
  }, []);

  // Fetch users and products when create order drawer opens
  useEffect(
    function () {
      if (createOrderVisible) {
        fetchUsers();
        fetchProducts();
      }
    },
    [createOrderVisible]
  );

  return React.createElement(
    PageContainer,
    { className: 'order-management' },
    React.createElement(
      Spin,
      { spinning: loading, tip: 'Đang tải dữ liệu...' },
      React.createElement(
        StatsGrid,
        null,
        statsData.map(function (stat, index) {
          return React.createElement(StatsCard, {
            key: index,
            title: stat.title,
            value: stat.value,
            icon: React.createElement(stat.icon),
            color: stat.color,
          });
        })
      ),
      React.createElement(
        Card,
        null,
        React.createElement(PageHeader, {
          title: 'Danh sách đơn hàng',
          description: 'Xem và quản lý danh sách đơn hàng',
          actions: React.createElement(
            Space,
            null,
            React.createElement(
              AntButton,
              {
                icon: React.createElement(RefreshCw, { size: 16 }),
                onClick: handleRefresh,
                loading: refreshing,
                style: {
                  borderRadius: '6px',
                  fontWeight: 500,
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                },
              },
              'Làm mới'
            ),
            React.createElement(
              AntButton,
              {
                type: 'default',
                icon: React.createElement(Download, { size: 16 }),
                onClick: function () {
                  return setExportModalVisible(true);
                },
                style: {
                  borderRadius: '6px',
                  fontWeight: 500,
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                },
              },
              'Xuất Excel'
            ),
            React.createElement(
              AntButton,
              {
                type: 'primary',
                icon: React.createElement(ShoppingCart, { size: 16 }),
                onClick: function () {
                  return setCreateOrderVisible(true);
                },
                style: {
                  borderRadius: '6px',
                  fontWeight: 500,
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                },
              },
              'Tạo đơn hàng'
            )
          ),
        }),
        React.createElement(
          CardContent,
          null,
          React.createElement(
            FiltersSection,
            null,
            React.createElement(SearchBar, {
              placeholder: 'Tìm theo mã đơn hàng...',
              value: filters.search,
              onChange: function (value) {
                setFilters(function (prev) {
                  return { ...prev, search: value };
                });
              },
              onSearch: function () {
                return fetchOrders();
              },
              allowClear: true,
              style: { width: '100%' },
              size: 'large',
            })
          ),
          React.createElement(Tabs, {
            activeKey: filters.status || 'all',
            onChange: function (key) {
              const status = key === 'all' ? '' : key;
              setFilters(function (prev) {
                return { ...prev, status: status };
              });
              setPagination(function (prev) {
                return { ...prev, page: 1 };
              });
            },
            items: [
              {
                key: 'all',
                label: 'Tất cả',
              },
              {
                key: 'Chờ xác nhận',
                label: 'Chờ xác nhận',
              },
              {
                key: 'Đã xác nhận',
                label: 'Đã xác nhận',
              },
              {
                key: 'Chờ giao hàng',
                label: 'Chờ giao hàng',
              },
              {
                key: 'Đang giao hàng',
                label: 'Đang giao hàng',
              },
              {
                key: 'Hoàn thành',
                label: 'Hoàn thành',
              },
              {
                key: 'Đã hủy',
                label: 'Đã hủy',
              },
            ],
          }),
          React.createElement(AntTable, {
            columns: [
              {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
                width: 80,
              },
              {
                title: 'Mã đơn hàng',
                dataIndex: 'code',
                key: 'code',
                width: 150,
              },
              {
                title: 'Trạng thái',
                dataIndex: 'statusOrder',
                key: 'statusOrder',
                width: 150,
                render: function (status) {
                  const config = getStatusConfig(status);
                  return React.createElement(
                    Tag,
                    {
                      color: config.color,
                      icon: config.icon,
                    },
                    status
                  );
                },
              },
              {
                title: 'Khách hàng',
                key: 'customer',
                width: 200,
                render: function (_, record) {
                  return React.createElement(
                    'div',
                    null,
                    React.createElement(
                      'div',
                      { style: { fontWeight: 500 } },
                      record.customer?.name || ''
                    ),
                    React.createElement(
                      'div',
                      { style: { fontSize: '12px', color: '#666' } },
                      record.customer?.phone || ''
                    )
                  );
                },
              },
              {
                title: 'Tổng tiền',
                dataIndex: 'priceAfterTax',
                key: 'priceAfterTax',
                width: 150,
                render: function (amount) {
                  return React.createElement(
                    'span',
                    { style: { fontWeight: 500, color: '#059669' } },
                    formatCurrency(amount) + ' VNĐ'
                  );
                },
              },
              {
                title: 'Thao tác',
                key: 'actions',
                width: 200,
                render: function (_, record) {
                  return React.createElement(
                    Space,
                    { size: 'small' },
                    React.createElement(
                      AntButton,
                      {
                        type: 'link',
                        size: 'small',
                        onClick: function () {
                          handleViewOrder(record.id);
                        },
                      },
                      'Xem'
                    ),
                    React.createElement(StatusUpdateButton, {
                      currentStatus: record.statusOrder,
                      orderId: record.id,
                      orderCode: record.code,
                    })
                  );
                },
              },
            ],
            dataSource: orders,
            rowKey: 'id',
            loading: loading,
            pagination: {
              current: pagination.page,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: function (total, range) {
                return (
                  range[0] + '-' + range[1] + ' của ' + total + ' đơn hàng'
                );
              },
              onChange: function (page, pageSize) {
                setPagination(function (prev) {
                  return { ...prev, page: page, pageSize: pageSize };
                });
              },
              onShowSizeChange: function (current, size) {
                setPagination(function (prev) {
                  return { ...prev, page: 1, pageSize: size };
                });
              },
            },
            locale: {
              emptyText: React.createElement(Empty, {
                description: 'Không có đơn hàng nào',
              }),
            },
            scroll: { x: 1200 },
            size: 'middle',
            bordered: true,
            style: {
              borderRadius: '8px',
              overflow: 'hidden',
            },
          }),

          // Export Modal
          React.createElement(
            Modal,
            {
              title: 'Xuất Excel',
              open: exportModalVisible,
              onOk: handleExportConfirm,
              onCancel: function () {
                setExportModalVisible(false);
                setExportDateRange(null);
                setExportStatuses([]);
              },
              confirmLoading: exporting,
              okText: 'Xuất Excel',
              cancelText: 'Hủy',
              width: 600,
            },
            React.createElement(
              'div',
              { style: { padding: '20px 0' } },
              React.createElement(
                'div',
                { style: { marginBottom: '16px' } },
                React.createElement(
                  'label',
                  {
                    style: {
                      fontWeight: 500,
                      marginBottom: '8px',
                      display: 'block',
                    },
                  },
                  'Chọn khoảng thời gian:'
                ),
                React.createElement(RangePicker, {
                  value: exportDateRange,
                  onChange: setExportDateRange,
                  style: { width: '100%' },
                  placeholder: ['Từ ngày', 'Đến ngày'],
                })
              ),
              React.createElement(
                'div',
                null,
                React.createElement(
                  'label',
                  {
                    style: {
                      fontWeight: 500,
                      marginBottom: '8px',
                      display: 'block',
                    },
                  },
                  'Chọn trạng thái:'
                ),
                React.createElement(
                  Checkbox.Group,
                  {
                    value: exportStatuses,
                    onChange: setExportStatuses,
                    style: { width: '100%' },
                  },
                  React.createElement(
                    Row,
                    null,
                    statusOptions.map(function (option) {
                      return React.createElement(
                        Col,
                        { span: 12, key: option.value },
                        React.createElement(
                          Checkbox,
                          { value: option.value },
                          option.label
                        )
                      );
                    })
                  )
                )
              )
            )
          ),

          // Order Detail Modal
          React.createElement(
            Modal,
            {
              title: 'Chi tiết đơn hàng',
              open: orderDetailVisible,
              onCancel: function () {
                setOrderDetailVisible(false);
                setOrderDetail(null);
              },
              footer: null,
              width: 800,
            },
            orderDetailLoading
              ? React.createElement(
                  'div',
                  { style: { textAlign: 'center', padding: '50px' } },
                  React.createElement(Spin, { size: 'large' })
                )
              : orderDetail
              ? React.createElement(
                  'div',
                  null,
                  React.createElement(
                    'h3',
                    null,
                    'Đơn hàng: ',
                    orderDetail.code
                  ),
                  React.createElement(
                    'p',
                    null,
                    React.createElement('strong', null, 'Khách hàng: '),
                    orderDetail.customer?.name
                  ),
                  React.createElement(
                    'p',
                    null,
                    React.createElement('strong', null, 'Trạng thái: '),
                    orderDetail.statusOrder
                  ),
                  React.createElement(
                    'p',
                    null,
                    React.createElement('strong', null, 'Tổng tiền: '),
                    formatCurrency(orderDetail.priceAfterTax),
                    ' VNĐ'
                  )
                )
              : null
          ),

          // Create Order Drawer
          React.createElement(
            Drawer,
            {
              title: React.createElement(
                'span',
                {
                  style: {
                    fontSize: '18px',
                    fontWeight: 600,
                    color: '#374151',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8,
                  },
                },
                'Tạo đơn hàng mới'
              ),
              placement: 'right',
              width: '60%',
              open: createOrderVisible,
              onClose: function () {
                setCreateOrderVisible(false);
                resetCustomerForm();
                setSelectedProducts([]);
              },
              footer: React.createElement(
                'div',
                {
                  style: {
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: 12,
                    padding: '16px 24px',
                    borderTop: '1px solid #e2e8f0',
                  },
                },
                React.createElement(
                  AntButton,
                  {
                    onClick: function () {
                      setCreateOrderVisible(false);
                    },
                    disabled: creating,
                    style: {
                      borderRadius: '6px',
                      fontWeight: 500,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    },
                  },
                  'Hủy'
                ),
                React.createElement(
                  AntButton,
                  {
                    type: 'primary',
                    loading: creating,
                    icon: React.createElement(Plus, { size: 16 }),
                    onClick: handleCreateOrder,
                    style: {
                      borderRadius: '6px',
                      fontWeight: 500,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                      background: '#2563eb',
                      borderColor: '#2563eb',
                    },
                  },
                  'Tạo đơn hàng'
                )
              ),
            },
            React.createElement(
              'div',
              { style: { fontFamily: "'Be Vietnam Pro', sans-serif" } },
              React.createElement(
                'div',
                {
                  style: {
                    marginBottom: 24,
                    padding: 16,
                    background: '#f8fafc',
                    borderRadius: 8,
                    border: '1px solid #e2e8f0',
                  },
                },
                React.createElement(
                  'div',
                  {
                    style: { fontSize: 14, color: '#64748b', marginBottom: 8 },
                  },
                  '📝 Hướng dẫn tạo đơn hàng'
                ),
                React.createElement(
                  'div',
                  {
                    style: { fontSize: 13, color: '#64748b', lineHeight: 1.5 },
                  },
                  'Điền đầy đủ thông tin khách hàng và sản phẩm để tạo đơn hàng mới. Tất cả các trường có dấu (*) là bắt buộc.'
                )
              ),

              // Customer Information Section
              React.createElement(
                'div',
                { style: { marginBottom: 24 } },
                React.createElement(
                  'div',
                  {
                    style: {
                      fontSize: 16,
                      fontWeight: 600,
                      marginBottom: 16,
                      color: '#374151',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                    },
                  },
                  React.createElement('div', {
                    style: {
                      width: 4,
                      height: 20,
                      background: '#2563eb',
                      borderRadius: 2,
                    },
                  }),
                  'Thông tin khách hàng'
                ),

                // User Selection
                React.createElement(
                  'div',
                  { style: { marginBottom: 16 } },
                  React.createElement(
                    'label',
                    {
                      style: {
                        display: 'block',
                        marginBottom: 8,
                        fontWeight: 500,
                        color: '#374151',
                      },
                    },
                    'Chọn từ danh sách đại lý'
                  ),
                  React.createElement(Select, {
                    placeholder:
                      users.length === 0 && !usersLoading
                        ? 'Không có đại lý nào trong hệ thống'
                        : 'Chọn đại lý có sẵn hoặc nhập thông tin mới',
                    style: { width: '100%' },
                    size: 'large',
                    loading: usersLoading,
                    allowClear: true,
                    showSearch: true,
                    filterOption: function (input, option) {
                      return (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase());
                    },
                    value: selectedUserId,
                    onChange: function (value) {
                      if (value) {
                        handleUserSelect(value);
                      } else {
                        resetCustomerForm();
                      }
                    },
                    options: users.map(function (user) {
                      return {
                        value: user.id,
                        label: user.name + ' - ' + user.phone,
                      };
                    }),
                    notFoundContent: usersLoading
                      ? 'Đang tải...'
                      : 'Không có dữ liệu',
                  })
                ),

                // Customer Form Fields
                React.createElement(
                  'div',
                  {
                    style: {
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 16,
                      marginBottom: 16,
                    },
                  },
                  React.createElement(
                    'div',
                    null,
                    React.createElement(
                      'label',
                      {
                        style: {
                          display: 'block',
                          marginBottom: 8,
                          fontWeight: 500,
                          color: '#374151',
                        },
                      },
                      'Tên khách hàng ',
                      React.createElement(
                        'span',
                        { style: { color: '#ef4444' } },
                        '*'
                      )
                    ),
                    React.createElement(Input, {
                      placeholder: 'Nhập tên khách hàng',
                      size: 'large',
                      value: customerFormData.name,
                      onChange: function (e) {
                        setCustomerFormData(function (prev) {
                          return { ...prev, name: e.target.value };
                        });
                      },
                    })
                  ),
                  React.createElement(
                    'div',
                    null,
                    React.createElement(
                      'label',
                      {
                        style: {
                          display: 'block',
                          marginBottom: 8,
                          fontWeight: 500,
                          color: '#374151',
                        },
                      },
                      'Số điện thoại ',
                      React.createElement(
                        'span',
                        { style: { color: '#ef4444' } },
                        '*'
                      )
                    ),
                    React.createElement(Input, {
                      placeholder: 'Nhập số điện thoại',
                      size: 'large',
                      value: customerFormData.phone,
                      onChange: function (e) {
                        setCustomerFormData(function (prev) {
                          return { ...prev, phone: e.target.value };
                        });
                      },
                    })
                  )
                ),

                // Email Field
                React.createElement(
                  'div',
                  { style: { marginBottom: 16 } },
                  React.createElement(
                    'label',
                    {
                      style: {
                        display: 'block',
                        marginBottom: 8,
                        fontWeight: 500,
                        color: '#374151',
                      },
                    },
                    'Email'
                  ),
                  React.createElement(Input, {
                    type: 'email',
                    placeholder: 'Nhập email khách hàng',
                    size: 'large',
                    value: customerFormData.email,
                    onChange: function (e) {
                      setCustomerFormData(function (prev) {
                        return { ...prev, email: e.target.value };
                      });
                    },
                  })
                ),

                // Address Field
                React.createElement(
                  'div',
                  null,
                  React.createElement(
                    'label',
                    {
                      style: {
                        display: 'block',
                        marginBottom: 8,
                        fontWeight: 500,
                        color: '#374151',
                      },
                    },
                    'Địa chỉ ',
                    React.createElement(
                      'span',
                      { style: { color: '#ef4444' } },
                      '*'
                    )
                  ),
                  React.createElement(Input.TextArea, {
                    placeholder: 'Nhập địa chỉ đầy đủ',
                    rows: 3,
                    size: 'large',
                    value: customerFormData.address,
                    onChange: function (e) {
                      setCustomerFormData(function (prev) {
                        return { ...prev, address: e.target.value };
                      });
                    },
                    style: { resize: 'vertical' },
                  })
                )
              ),

              // Order Information Section
              React.createElement(
                'div',
                { style: { marginBottom: 24 } },
                React.createElement(
                  'div',
                  {
                    style: {
                      fontSize: 16,
                      fontWeight: 600,
                      marginBottom: 16,
                      color: '#374151',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                    },
                  },
                  React.createElement('div', {
                    style: {
                      width: 4,
                      height: 20,
                      background: '#059669',
                      borderRadius: 2,
                    },
                  }),
                  'Thông tin đơn hàng'
                ),

                // Product Selection
                React.createElement(
                  'div',
                  { style: { marginBottom: 16 } },
                  React.createElement(
                    'label',
                    {
                      style: {
                        display: 'block',
                        marginBottom: 8,
                        fontWeight: 500,
                        color: '#374151',
                      },
                    },
                    'Chọn sản phẩm ',
                    React.createElement(
                      'span',
                      { style: { color: '#ef4444' } },
                      '*'
                    )
                  ),
                  React.createElement(AutoComplete, {
                    placeholder: 'Tìm và chọn sản phẩm',
                    style: { width: '100%' },
                    size: 'large',
                    value: productSearchTerm,
                    onSearch: function (value) {
                      setProductSearchTerm(value);
                      if (value.length >= 1 || value.length === 0) {
                        fetchProducts(value);
                      }
                    },
                    onChange: function (value) {
                      setProductSearchTerm(value);
                      if (value.length >= 1 || value.length === 0) {
                        fetchProducts(value);
                      }
                    },
                    options: products.map(function (product) {
                      return {
                        value: product.id.toString(),
                        label: React.createElement(
                          'div',
                          {
                            style: {
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              padding: '8px 0',
                            },
                          },
                          React.createElement(
                            'div',
                            {
                              style: {
                                width: 40,
                                height: 40,
                                borderRadius: 6,
                                overflow: 'hidden',
                                border: '1px solid #e2e8f0',
                                backgroundColor: '#ffffff',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0,
                              },
                            },
                            product.imageUrl
                              ? React.createElement('img', {
                                  src: product.imageUrl,
                                  alt: product.name,
                                  style: {
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                  },
                                })
                              : React.createElement(
                                  'span',
                                  {
                                    style: {
                                      fontSize: '16px',
                                      color: '#94a3b8',
                                    },
                                  },
                                  '📦'
                                )
                          ),
                          React.createElement(
                            'div',
                            { style: { flex: 1, minWidth: 0 } },
                            React.createElement(
                              'div',
                              {
                                style: {
                                  fontWeight: 500,
                                  color: '#374151',
                                  fontSize: '14px',
                                  marginBottom: 4,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                },
                              },
                              product.name
                            ),
                            React.createElement(
                              'div',
                              {
                                style: {
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 8,
                                  fontSize: '13px',
                                },
                              },
                              React.createElement(
                                'span',
                                {
                                  style: {
                                    color: '#059669',
                                    fontWeight: 600,
                                  },
                                },
                                formatCurrency(product.gia_ban || 0)
                              ),
                              product.so_luong_ton_kho !== undefined &&
                                React.createElement(
                                  'span',
                                  {
                                    style: {
                                      color:
                                        product.so_luong_ton_kho > 0
                                          ? '#64748b'
                                          : '#ef4444',
                                      fontSize: '12px',
                                    },
                                  },
                                  'Kho: ' + product.so_luong_ton_kho
                                )
                            )
                          ),
                          React.createElement(AntButton, {
                            type: 'primary',
                            size: 'small',
                            icon: React.createElement(Plus, { size: 14 }),
                            style: {
                              borderRadius: 6,
                              height: 32,
                              width: 32,
                              padding: 0,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              background: '#2563eb',
                              borderColor: '#2563eb',
                              flexShrink: 0,
                            },
                            onClick: function (e) {
                              e.stopPropagation();
                              handleProductSelect(product.id, 1);
                              setProductSearchTerm('');
                            },
                          })
                        ),
                      };
                    }),
                  })
                )
              )
            )
          )
        )
      )
    )
  );
};

module.exports = OrderManagement;
